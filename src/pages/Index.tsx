import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ArrowRight,
  Settings,
  Table,
  Users,
  Code,
  TerminalSquare,
} from "lucide-react";
import { Link } from "react-router-dom";

const quickLinks = [
  {
    title: "Configure Database",
    description: "Set up your PlanetScale connection.",
    href: "/configuration",
    icon: Settings,
    color: "text-blue-500",
  },
  {
    title: "View Tables",
    description: "Browse your database tables.",
    href: "/tables",
    icon: Table,
    color: "text-green-500",
  },
  {
    title: "Manage Teams",
    description: "Control user access.",
    href: "/teams",
    icon: Users,
    color: "text-purple-500",
  },
  {
    title: "SQL Editor",
    description: "Run custom SQL queries.",
    href: "/sql-editor",
    icon: Code,
    color: "text-red-500",
  },
  {
    title: "Edge Functions",
    description: "Manage serverless functions.",
    href: "/edge-functions",
    icon: TerminalSquare,
    color: "text-yellow-500",
  },
];

const IndexPage = () => {
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Getting Started</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-gray-700">
          <p>
            1. Navigate to the{" "}
            <Link
              to="/configuration"
              className="text-blue-600 hover:underline font-medium"
            >
              Configuration
            </Link>{" "}
            page to set up your database connection details.
          </p>
          <p>
            2. Once configured, you can explore your{" "}
            <Link
              to="/tables"
              className="text-blue-600 hover:underline font-medium"
            >
              Tables
            </Link>
            , manage{" "}
            <Link
              to="/teams"
              className="text-blue-600 hover:underline font-medium"
            >
              Teams
            </Link>
            , or run queries in the{" "}
            <Link
              to="/sql-editor"
              className="text-blue-600 hover:underline font-medium"
            >
              SQL Editor
            </Link>
            .
          </p>
          <p>
            3. Check out{" "}
            <Link
              to="/edge-functions"
              className="text-blue-600 hover:underline font-medium"
            >
              Edge Functions
            </Link>{" "}
            to manage your serverless logic.
          </p>
        </CardContent>
      </Card>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {quickLinks.map((link) => (
          <Card key={link.title} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg font-medium">
                {link.title}
              </CardTitle>
              <link.icon className={`h-6 w-6 ${link.color}`} />
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500 mb-4">{link.description}</p>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link to={link.href}>
                  Go to {link.title.split(" ")[0]}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default IndexPage;
