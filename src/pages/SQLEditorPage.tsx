import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Play, FileText, Loader2 } from "lucide-react";
import { useState } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

const SQLEditorPage = () => {
  const [sqlQuery, setSqlQuery] = useState("SELECT * FROM users LIMIT 10;");
  const [results, setResults] = useState<Record<string, any>[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleRunQuery = () => {
    setIsLoading(true);
    setError(null);
    setResults(null);
    // Simulate API call
    setTimeout(() => {
      if (sqlQuery.toLowerCase().includes("error")) {
        setError("Simulated SQL Error: Invalid syntax near 'error'.");
      } else if (sqlQuery.trim() === "SELECT * FROM users LIMIT 10;") {
         setResults([
          { id: 1, name: 'Alice Smith', email: '<EMAIL>', age: 30 },
          { id: 2, name: 'Bob Johnson', email: '<EMAIL>', age: 24 },
          { id: 3, name: 'Charlie Davis', email: '<EMAIL>', age: 35 },
        ]);
      } else {
        setResults([]); // Empty result for other queries
      }
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-gray-800">SQL Editor</h1>
        <Button onClick={handleRunQuery} disabled={isLoading}>
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Play className="mr-2 h-4 w-4" />
          )}
          Run Query
        </Button>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Query Input</CardTitle>
          <CardDescription>Write your SQL query below. Results will appear underneath.</CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="SELECT * FROM your_table;"
            className="min-h-[200px] font-mono text-sm"
            value={sqlQuery}
            onChange={(e) => setSqlQuery(e.target.value)}
          />
        </CardContent>
      </Card>

      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="ml-2 text-gray-600">Executing query...</p>
        </div>
      )}

      {error && (
        <Card className="border-red-500">
          <CardHeader>
            <CardTitle className="text-red-700">Query Error</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm text-red-600 bg-red-50 p-3 rounded-md">{error}</pre>
          </CardContent>
        </Card>
      )}

      {results && !error && (
        <Card>
          <CardHeader>
            <CardTitle>Results</CardTitle>
            <CardDescription>{results.length} row(s) returned.</CardDescription>
          </CardHeader>
          <CardContent>
            {results.length > 0 ? (
              <div className="max-h-[400px] overflow-auto">
                <Table>
                  <TableHeader className="sticky top-0 bg-white">
                    <TableRow>
                      {Object.keys(results[0]).map((key) => (
                        <TableHead key={key}>{key}</TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {results.map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {Object.values(row).map((value, cellIndex) => (
                          <TableCell key={cellIndex}>{String(value)}</TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <FileText className="h-16 w-16 text-gray-400 mb-4" />
                <p className="text-lg font-medium text-gray-600">Query executed successfully, but returned no rows.</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SQLEditorPage;