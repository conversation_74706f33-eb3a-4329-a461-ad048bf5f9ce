import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { PlusCircle, FileCode, Play, Save, Trash2 } from "lucide-react";
import { useState } from "react";
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";

const mockFunctions = [
  { id: "1", name: "sendWelcomeEmail.ts", code: "export default async (req, res) => {\n  // Your function logic here\n  res.status(200).send('Welcome email sent!');\n};" },
  { id: "2", name: "processPayment.ts", code: "export default async (req, res) => {\n  const { amount } = req.body;\n  // Process payment\n  res.status(200).json({ success: true, amount });\n};" },
  { id: "3", name: "resizeImage.ts", code: "export default async (req, res) => {\n  // Image resizing logic\n  res.status(200).send('Image resized!');\n};" },
];

const EdgeFunctionPage = () => {
  const [selectedFunction, setSelectedFunction] = useState(mockFunctions[0] || null);
  const [code, setCode] = useState(selectedFunction?.code || "");

  const handleSelectFunction = (func) => {
    setSelectedFunction(func);
    setCode(func.code);
  };

  return (
    <div className="space-y-6 h-[calc(100vh-10rem)] flex flex-col">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-gray-800">Edge Functions</h1>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          New Function
        </Button>
      </div>

      <ResizablePanelGroup direction="horizontal" className="flex-grow rounded-lg border">
        <ResizablePanel defaultSize={25} minSize={20} maxSize={40}>
          <div className="flex h-full flex-col">
            <CardHeader className="p-4 border-b">
              <CardTitle className="text-lg">Functions</CardTitle>
            </CardHeader>
            <div className="flex-grow p-2 space-y-1 overflow-y-auto">
              {mockFunctions.map((func) => (
                <Button
                  key={func.id}
                  variant={selectedFunction?.id === func.id ? "secondary" : "ghost"}
                  className="w-full justify-start"
                  onClick={() => handleSelectFunction(func)}
                >
                  <FileCode className="mr-2 h-4 w-4" />
                  {func.name}
                </Button>
              ))}
            </div>
          </div>
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={75}>
          {selectedFunction ? (
            <div className="flex h-full flex-col">
              <CardHeader className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{selectedFunction.name}</CardTitle>
                    <CardDescription>Edit your Edge Function code below.</CardDescription>
                  </div>
                  <div className="space-x-2">
                     <Button variant="outline" size="sm">
                      <Play className="mr-1.5 h-3.5 w-3.5" />
                      Test
                    </Button>
                    <Button size="sm">
                      <Save className="mr-1.5 h-3.5 w-3.5" />
                      Save
                    </Button>
                     <Button variant="destructive" size="sm">
                      <Trash2 className="mr-1.5 h-3.5 w-3.5" />
                      Delete
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0 flex-grow">
                <Textarea
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder="// Your Edge Function code here"
                  className="h-full w-full resize-none border-0 rounded-none focus-visible:ring-0 font-mono text-sm"
                />
              </CardContent>
            </div>
          ) : (
            <div className="flex h-full items-center justify-center">
              <div className="text-center">
                <FileCode className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No function selected</h3>
                <p className="mt-1 text-sm text-gray-500">Select a function from the list or create a new one.</p>
              </div>
            </div>
          )}
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};

export default EdgeFunctionPage;