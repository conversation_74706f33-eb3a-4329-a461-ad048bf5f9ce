import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Layout from "./components/Layout";
import IndexPage from "./pages/Index"; // Renamed for clarity if needed, or use existing Index
import ConfigurationPage from "./pages/ConfigurationPage";
import TablesPage from "./pages/TablesPage";
import TeamsPage from "./pages/TeamsPage";
import SQLEditorPage from "./pages/SQLEditorPage";
import EdgeFunctionPage from "./pages/EdgeFunctionPage";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route element={<Layout />}>
            <Route path="/" element={<IndexPage />} />
            <Route path="/configuration" element={<ConfigurationPage />} />
            <Route path="/tables" element={<TablesPage />} />
            <Route path="/teams" element={<TeamsPage />} />
            <Route path="/sql-editor" element={<SQLEditorPage />} />
            <Route path="/edge-functions" element={<EdgeFunctionPage />} />
            {/* Optional: Redirect base path to a default page like configuration */}
            {/* <Route path="/" element={<Navigate to="/configuration" replace />} /> */}
          </Route>
          {/* ADD ALL CUSTOM ROUTES INSIDE THE LAYOUT ROUTE IF THEY SHOULD HAVE THE NAVBAR */}
          {/* For pages without the main layout, define them outside: */}
          {/* <Route path="/login" element={<LoginPage />} /> */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;